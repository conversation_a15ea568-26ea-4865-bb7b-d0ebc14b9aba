  Manifest android  ACCESS_BACKGROUND_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  let android.app.Activity  VISIBILITY_PRIVATE android.app.Notification  Notification android.app.NotificationChannel  apply android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  IMPORTANCE_NONE android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  getActivity android.app.PendingIntent  ActivityCompat android.app.Service  ActivityNotFoundException android.app.Service  BackgroundNotification android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  FlutterLocation android.app.Service  Log android.app.Service  Manifest android.app.Service  ONGOING_NOTIFICATION_ID android.app.Service  PackageManager android.app.Service   REQUEST_PERMISSIONS_REQUEST_CODE android.app.Service  STOP_FOREGROUND_REMOVE android.app.Service  Suppress android.app.Service  TAG android.app.Service  arrayOf android.app.Service  let android.app.Service  mapOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  onUnbind android.app.Service  startForeground android.app.Service  stopForeground android.app.Service  to android.app.Service  ActivityNotFoundException android.content  Context android.content  Intent android.content  ActivityCompat android.content.Context  ActivityNotFoundException android.content.Context  BackgroundNotification android.content.Context  Build android.content.Context  
CHANNEL_ID android.content.Context  FlutterLocation android.content.Context  Log android.content.Context  Manifest android.content.Context  ONGOING_NOTIFICATION_ID android.content.Context  PackageManager android.content.Context   REQUEST_PERMISSIONS_REQUEST_CODE android.content.Context  STOP_FOREGROUND_REMOVE android.content.Context  Suppress android.content.Context  TAG android.content.Context  arrayOf android.content.Context  let android.content.Context  mapOf android.content.Context  packageManager android.content.Context  packageName android.content.Context  	resources android.content.Context  to android.content.Context  ActivityCompat android.content.ContextWrapper  ActivityNotFoundException android.content.ContextWrapper  BackgroundNotification android.content.ContextWrapper  Build android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  FlutterLocation android.content.ContextWrapper  Log android.content.ContextWrapper  Manifest android.content.ContextWrapper  ONGOING_NOTIFICATION_ID android.content.ContextWrapper  PackageManager android.content.ContextWrapper   REQUEST_PERMISSIONS_REQUEST_CODE android.content.ContextWrapper  STOP_FOREGROUND_REMOVE android.content.ContextWrapper  Suppress android.content.ContextWrapper  TAG android.content.ContextWrapper  applicationContext android.content.ContextWrapper  arrayOf android.content.ContextWrapper  let android.content.ContextWrapper  mapOf android.content.ContextWrapper  to android.content.ContextWrapper  FLAG_ACTIVITY_NEW_TASK android.content.Intent  "FLAG_ACTIVITY_RESET_TASK_IF_NEEDED android.content.Intent  setFlags android.content.Intent  
setPackage android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  
getIdentifier android.content.res.Resources  Binder 
android.os  Build 
android.os  IBinder 
android.os  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  Log android.util  d android.util.Log  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  $shouldShowRequestPermissionRationale  androidx.core.app.ActivityCompat  Builder $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setColor ,androidx.core.app.NotificationCompat.Builder  setColorized ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
setSubText ,androidx.core.app.NotificationCompat.Builder  createNotificationChannel +androidx.core.app.NotificationManagerCompat  from +androidx.core.app.NotificationManagerCompat  notify +androidx.core.app.NotificationManagerCompat  checkSelfPermission #androidx.core.content.ContextCompat  Activity com.lyokone.location  ActivityCompat com.lyokone.location  ActivityNotFoundException com.lyokone.location  Any com.lyokone.location  Array com.lyokone.location  BackgroundNotification com.lyokone.location  Binder com.lyokone.location  Boolean com.lyokone.location  Build com.lyokone.location  
CHANNEL_ID com.lyokone.location  Context com.lyokone.location  FlutterLocation com.lyokone.location  FlutterLocationService com.lyokone.location  IBinder com.lyokone.location  Int com.lyokone.location  IntArray com.lyokone.location  Intent com.lyokone.location  Log com.lyokone.location  Manifest com.lyokone.location  Map com.lyokone.location  
MethodChannel com.lyokone.location  Notification com.lyokone.location  NotificationChannel com.lyokone.location  NotificationCompat com.lyokone.location  NotificationManager com.lyokone.location  NotificationManagerCompat com.lyokone.location  NotificationOptions com.lyokone.location  ONGOING_NOTIFICATION_ID com.lyokone.location  PackageManager com.lyokone.location  
PendingIntent com.lyokone.location  PluginRegistry com.lyokone.location   REQUEST_PERMISSIONS_REQUEST_CODE com.lyokone.location  STOP_FOREGROUND_REMOVE com.lyokone.location  Service com.lyokone.location  String com.lyokone.location  Suppress com.lyokone.location  TAG com.lyokone.location  apply com.lyokone.location  arrayOf com.lyokone.location  kDefaultChannelName com.lyokone.location  kDefaultNotificationIconName com.lyokone.location  kDefaultNotificationTitle com.lyokone.location  let com.lyokone.location  mapOf com.lyokone.location  to com.lyokone.location  Build +com.lyokone.location.BackgroundNotification  Intent +com.lyokone.location.BackgroundNotification  Notification +com.lyokone.location.BackgroundNotification  NotificationChannel +com.lyokone.location.BackgroundNotification  NotificationCompat +com.lyokone.location.BackgroundNotification  NotificationManager +com.lyokone.location.BackgroundNotification  NotificationManagerCompat +com.lyokone.location.BackgroundNotification  NotificationOptions +com.lyokone.location.BackgroundNotification  
PendingIntent +com.lyokone.location.BackgroundNotification  apply +com.lyokone.location.BackgroundNotification  build +com.lyokone.location.BackgroundNotification  buildBringToFrontIntent +com.lyokone.location.BackgroundNotification  builder +com.lyokone.location.BackgroundNotification  	channelId +com.lyokone.location.BackgroundNotification  context +com.lyokone.location.BackgroundNotification  
getDrawableId +com.lyokone.location.BackgroundNotification  kDefaultNotificationIconName +com.lyokone.location.BackgroundNotification  let +com.lyokone.location.BackgroundNotification  notificationId +com.lyokone.location.BackgroundNotification  options +com.lyokone.location.BackgroundNotification  
updateChannel +com.lyokone.location.BackgroundNotification  updateNotification +com.lyokone.location.BackgroundNotification  
updateOptions +com.lyokone.location.BackgroundNotification  checkPermissions $com.lyokone.location.FlutterLocation  requestPermissions $com.lyokone.location.FlutterLocation  result $com.lyokone.location.FlutterLocation  setActivity $com.lyokone.location.FlutterLocation  Activity +com.lyokone.location.FlutterLocationService  ActivityCompat +com.lyokone.location.FlutterLocationService  ActivityNotFoundException +com.lyokone.location.FlutterLocationService  Any +com.lyokone.location.FlutterLocationService  Array +com.lyokone.location.FlutterLocationService  BackgroundNotification +com.lyokone.location.FlutterLocationService  Binder +com.lyokone.location.FlutterLocationService  Boolean +com.lyokone.location.FlutterLocationService  Build +com.lyokone.location.FlutterLocationService  
CHANNEL_ID +com.lyokone.location.FlutterLocationService  FlutterLocation +com.lyokone.location.FlutterLocationService  FlutterLocationService +com.lyokone.location.FlutterLocationService  IBinder +com.lyokone.location.FlutterLocationService  Int +com.lyokone.location.FlutterLocationService  IntArray +com.lyokone.location.FlutterLocationService  Intent +com.lyokone.location.FlutterLocationService  LocalBinder +com.lyokone.location.FlutterLocationService  Log +com.lyokone.location.FlutterLocationService  Manifest +com.lyokone.location.FlutterLocationService  Map +com.lyokone.location.FlutterLocationService  
MethodChannel +com.lyokone.location.FlutterLocationService  NotificationOptions +com.lyokone.location.FlutterLocationService  ONGOING_NOTIFICATION_ID +com.lyokone.location.FlutterLocationService  PackageManager +com.lyokone.location.FlutterLocationService  PluginRegistry +com.lyokone.location.FlutterLocationService   REQUEST_PERMISSIONS_REQUEST_CODE +com.lyokone.location.FlutterLocationService  STOP_FOREGROUND_REMOVE +com.lyokone.location.FlutterLocationService  String +com.lyokone.location.FlutterLocationService  Suppress +com.lyokone.location.FlutterLocationService  TAG +com.lyokone.location.FlutterLocationService  activity +com.lyokone.location.FlutterLocationService  applicationContext +com.lyokone.location.FlutterLocationService  arrayOf +com.lyokone.location.FlutterLocationService  backgroundNotification +com.lyokone.location.FlutterLocationService  binder +com.lyokone.location.FlutterLocationService  enableBackgroundMode +com.lyokone.location.FlutterLocationService  isForeground +com.lyokone.location.FlutterLocationService  let +com.lyokone.location.FlutterLocationService  location +com.lyokone.location.FlutterLocationService  mapOf +com.lyokone.location.FlutterLocationService  result +com.lyokone.location.FlutterLocationService  .shouldShowRequestBackgroundPermissionRationale +com.lyokone.location.FlutterLocationService  startForeground +com.lyokone.location.FlutterLocationService  stopForeground +com.lyokone.location.FlutterLocationService  to +com.lyokone.location.FlutterLocationService  ActivityCompat 5com.lyokone.location.FlutterLocationService.Companion  ActivityNotFoundException 5com.lyokone.location.FlutterLocationService.Companion  BackgroundNotification 5com.lyokone.location.FlutterLocationService.Companion  Build 5com.lyokone.location.FlutterLocationService.Companion  
CHANNEL_ID 5com.lyokone.location.FlutterLocationService.Companion  FlutterLocation 5com.lyokone.location.FlutterLocationService.Companion  Log 5com.lyokone.location.FlutterLocationService.Companion  Manifest 5com.lyokone.location.FlutterLocationService.Companion  ONGOING_NOTIFICATION_ID 5com.lyokone.location.FlutterLocationService.Companion  PackageManager 5com.lyokone.location.FlutterLocationService.Companion   REQUEST_PERMISSIONS_REQUEST_CODE 5com.lyokone.location.FlutterLocationService.Companion  STOP_FOREGROUND_REMOVE 5com.lyokone.location.FlutterLocationService.Companion  TAG 5com.lyokone.location.FlutterLocationService.Companion  arrayOf 5com.lyokone.location.FlutterLocationService.Companion  let 5com.lyokone.location.FlutterLocationService.Companion  mapOf 5com.lyokone.location.FlutterLocationService.Companion  to 5com.lyokone.location.FlutterLocationService.Companion  Result 9com.lyokone.location.FlutterLocationService.MethodChannel  ActivityResultListener :com.lyokone.location.FlutterLocationService.PluginRegistry   RequestPermissionsResultListener :com.lyokone.location.FlutterLocationService.PluginRegistry  Result "com.lyokone.location.MethodChannel  Builder 'com.lyokone.location.NotificationCompat  channelName (com.lyokone.location.NotificationOptions  color (com.lyokone.location.NotificationOptions  description (com.lyokone.location.NotificationOptions  iconName (com.lyokone.location.NotificationOptions  onTapBringToFront (com.lyokone.location.NotificationOptions  subtitle (com.lyokone.location.NotificationOptions  title (com.lyokone.location.NotificationOptions  ActivityResultListener #com.lyokone.location.PluginRegistry   RequestPermissionsResultListener #com.lyokone.location.PluginRegistry  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  Result &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  Array kotlin  	Function1 kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  Suppress kotlin  apply kotlin  arrayOf kotlin  let kotlin  to kotlin  get kotlin.Array  size kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Int  let 
kotlin.Int  or 
kotlin.Int  get kotlin.IntArray  to 
kotlin.String  Map kotlin.collections  mapOf kotlin.collections                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     