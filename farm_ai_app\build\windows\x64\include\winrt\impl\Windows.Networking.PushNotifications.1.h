// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Networking_PushNotifications_1_H
#define WINRT_Windows_Networking_PushNotifications_1_H
#include "winrt/impl/Windows.Networking.PushNotifications.0.h"
WINRT_EXPORT namespace winrt::Windows::Networking::PushNotifications
{
    struct __declspec(empty_bases) IPushNotificationChannel :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannel>
    {
        IPushNotificationChannel(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannel(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelManagerForUser :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelManagerForUser>
    {
        IPushNotificationChannelManagerForUser(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelManagerForUser(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelManagerForUser2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelManagerForUser2>
    {
        IPushNotificationChannelManagerForUser2(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelManagerForUser2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelManagerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelManagerStatics>
    {
        IPushNotificationChannelManagerStatics(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelManagerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelManagerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelManagerStatics2>
    {
        IPushNotificationChannelManagerStatics2(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelManagerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelManagerStatics3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelManagerStatics3>
    {
        IPushNotificationChannelManagerStatics3(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelManagerStatics3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelManagerStatics4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelManagerStatics4>
    {
        IPushNotificationChannelManagerStatics4(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelManagerStatics4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationChannelsRevokedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationChannelsRevokedEventArgs>
    {
        IPushNotificationChannelsRevokedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationChannelsRevokedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IPushNotificationReceivedEventArgs :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IPushNotificationReceivedEventArgs>
    {
        IPushNotificationReceivedEventArgs(std::nullptr_t = nullptr) noexcept {}
        IPushNotificationReceivedEventArgs(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRawNotification :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRawNotification>
    {
        IRawNotification(std::nullptr_t = nullptr) noexcept {}
        IRawNotification(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRawNotification2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRawNotification2>
    {
        IRawNotification2(std::nullptr_t = nullptr) noexcept {}
        IRawNotification2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IRawNotification3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IRawNotification3>
    {
        IRawNotification3(std::nullptr_t = nullptr) noexcept {}
        IRawNotification3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
