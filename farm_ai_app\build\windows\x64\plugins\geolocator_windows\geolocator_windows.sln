﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{1D6CF26A-A261-34B1-B24F-66FF322624FF}"
	ProjectSection(ProjectDependencies) = postProject
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC} = {C5289050-F0DF-3C40-A2AB-2604F843E3EC}
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5} = {24B2D996-51A6-34F0-AC45-EF51F0417BF5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{C91B2633-56EF-3B50-8F55-7D48F26FB18B}"
	ProjectSection(ProjectDependencies) = postProject
		{1D6CF26A-A261-34B1-B24F-66FF322624FF} = {1D6CF26A-A261-34B1-B24F-66FF322624FF}
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC} = {C5289050-F0DF-3C40-A2AB-2604F843E3EC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{C5289050-F0DF-3C40-A2AB-2604F843E3EC}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}"
	ProjectSection(ProjectDependencies) = postProject
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC} = {C5289050-F0DF-3C40-A2AB-2604F843E3EC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{4CC06770-F562-38B6-B279-2FFD9F015295}"
	ProjectSection(ProjectDependencies) = postProject
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC} = {C5289050-F0DF-3C40-A2AB-2604F843E3EC}
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472} = {EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "geolocator_windows_plugin", "geolocator_windows_plugin.vcxproj", "{24B2D996-51A6-34F0-AC45-EF51F0417BF5}"
	ProjectSection(ProjectDependencies) = postProject
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC} = {C5289050-F0DF-3C40-A2AB-2604F843E3EC}
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472} = {EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}
		{4CC06770-F562-38B6-B279-2FFD9F015295} = {4CC06770-F562-38B6-B279-2FFD9F015295}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1D6CF26A-A261-34B1-B24F-66FF322624FF}.Debug|x64.ActiveCfg = Debug|x64
		{1D6CF26A-A261-34B1-B24F-66FF322624FF}.Debug|x64.Build.0 = Debug|x64
		{1D6CF26A-A261-34B1-B24F-66FF322624FF}.Profile|x64.ActiveCfg = Profile|x64
		{1D6CF26A-A261-34B1-B24F-66FF322624FF}.Profile|x64.Build.0 = Profile|x64
		{1D6CF26A-A261-34B1-B24F-66FF322624FF}.Release|x64.ActiveCfg = Release|x64
		{1D6CF26A-A261-34B1-B24F-66FF322624FF}.Release|x64.Build.0 = Release|x64
		{C91B2633-56EF-3B50-8F55-7D48F26FB18B}.Debug|x64.ActiveCfg = Debug|x64
		{C91B2633-56EF-3B50-8F55-7D48F26FB18B}.Profile|x64.ActiveCfg = Profile|x64
		{C91B2633-56EF-3B50-8F55-7D48F26FB18B}.Release|x64.ActiveCfg = Release|x64
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC}.Debug|x64.ActiveCfg = Debug|x64
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC}.Debug|x64.Build.0 = Debug|x64
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC}.Profile|x64.ActiveCfg = Profile|x64
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC}.Profile|x64.Build.0 = Profile|x64
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC}.Release|x64.ActiveCfg = Release|x64
		{C5289050-F0DF-3C40-A2AB-2604F843E3EC}.Release|x64.Build.0 = Release|x64
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}.Debug|x64.ActiveCfg = Debug|x64
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}.Debug|x64.Build.0 = Debug|x64
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}.Profile|x64.ActiveCfg = Profile|x64
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}.Profile|x64.Build.0 = Profile|x64
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}.Release|x64.ActiveCfg = Release|x64
		{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}.Release|x64.Build.0 = Release|x64
		{4CC06770-F562-38B6-B279-2FFD9F015295}.Debug|x64.ActiveCfg = Debug|x64
		{4CC06770-F562-38B6-B279-2FFD9F015295}.Debug|x64.Build.0 = Debug|x64
		{4CC06770-F562-38B6-B279-2FFD9F015295}.Profile|x64.ActiveCfg = Profile|x64
		{4CC06770-F562-38B6-B279-2FFD9F015295}.Profile|x64.Build.0 = Profile|x64
		{4CC06770-F562-38B6-B279-2FFD9F015295}.Release|x64.ActiveCfg = Release|x64
		{4CC06770-F562-38B6-B279-2FFD9F015295}.Release|x64.Build.0 = Release|x64
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5}.Debug|x64.ActiveCfg = Debug|x64
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5}.Debug|x64.Build.0 = Debug|x64
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5}.Profile|x64.ActiveCfg = Profile|x64
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5}.Profile|x64.Build.0 = Profile|x64
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5}.Release|x64.ActiveCfg = Release|x64
		{24B2D996-51A6-34F0-AC45-EF51F0417BF5}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C95E2F30-4E5B-3715-B02A-E59540AF6500}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
