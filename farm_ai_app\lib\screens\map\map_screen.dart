import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import '../../services/location_service.dart';
import '../../services/field_service.dart';
import '../../models/field.dart';
import '../../widgets/location_search_bottom_sheet.dart';
import 'field_creation_screen.dart';
import 'field_details_screen.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final MapController _mapController = MapController();
  final LocationService _locationService = LocationService();
  final FieldService _fieldService = FieldService();

  Position? _currentPosition;
  List<Polygon> _polygons = [];
  List<Marker> _markers = [];
  List<Field> _fields = [];
  bool _isLoading = true;
  bool _isSatelliteView = false;

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    await _getCurrentLocation();
    await _loadFields();
    setState(() => _isLoading = false);
  }

  Future<void> _getCurrentLocation() async {
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        setState(() => _currentPosition = position);
      }
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  Future<void> _loadFields() async {
    try {
      final fields = await _fieldService.getUserFields();
      setState(() {
        _fields = fields;
        _updateMapPolygons();
      });
    } catch (e) {
      print('Error loading fields: $e');
    }
  }

  void _updateMapPolygons() {
    List<Polygon> polygons = [];
    List<Marker> markers = [];

    for (int i = 0; i < _fields.length; i++) {
      final field = _fields[i];

      // Convert FieldCoordinate to LatLng
      List<LatLng> points = field.areaCoordinates
          .map((coord) => LatLng(coord.latitude, coord.longitude))
          .toList();

      if (points.isNotEmpty) {
        // Create polygon
        polygons.add(
          Polygon(
            points: points,
            color: Colors.green.withOpacity(0.3),
            borderColor: Colors.green,
            borderStrokeWidth: 2,
          ),
        );

        // Create marker at center of field
        if (points.isNotEmpty) {
          double centerLat = points.map((p) => p.latitude).reduce((a, b) => a + b) / points.length;
          double centerLng = points.map((p) => p.longitude).reduce((a, b) => a + b) / points.length;

          markers.add(
            Marker(
              point: LatLng(centerLat, centerLng),
              width: 80,
              height: 80,
              child: GestureDetector(
                onTap: () => _showFieldDetails(field),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.agriculture,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          );
        }
      }
    }

    setState(() {
      _polygons = polygons;
      _markers = markers;
    });
  }

  void _showFieldDetails(Field field) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FieldDetailsScreen(field: field),
      ),
    ).then((_) => _loadFields()); // Refresh fields when returning
  }

  void _createNewField() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FieldCreationScreen(),
      ),
    ).then((_) => _loadFields()); // Refresh fields when returning
  }

  void _goToCurrentLocation() async {
    await _getCurrentLocation();
    if (_currentPosition != null) {
      _mapController.move(
        LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        15.0,
      );
    }
  }

  void _showLocationSearch() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => LocationSearchBottomSheet(
        onLocationSelected: (latitude, longitude, name) {
          _mapController.move(LatLng(latitude, longitude), 15.0);
          Navigator.pop(context);

          // Show info about selected location
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$name konumuna gidildi'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }

  void _toggleMapType() {
    setState(() {
      _isSatelliteView = !_isSatelliteView;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tarla Haritası'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showLocationSearch,
            tooltip: 'Konum Ara',
          ),
          IconButton(
            icon: Icon(_isSatelliteView ? Icons.map : Icons.satellite),
            onPressed: _toggleMapType,
            tooltip: 'Harita Türünü Değiştir',
          ),
          IconButton(
            icon: const Icon(Icons.my_location),
            onPressed: _goToCurrentLocation,
            tooltip: 'Konumuma Git',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                FlutterMap(
                  mapController: _mapController,
                  options: MapOptions(
                    initialCenter: _currentPosition != null
                        ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
                        : const LatLng(39.9334, 32.8597), // Ankara coordinates as default
                    initialZoom: 15.0,
                    minZoom: 3.0,
                    maxZoom: 20.0,
                    // Zoom ve pan kontrolleri
                    interactionOptions: const InteractionOptions(
                      flags: InteractiveFlag.all,
                      enableMultiFingerGestureRace: true,
                    ),
                    // Zoom animasyonu
                    onTap: (tapPosition, point) {
                      // Haritaya tıklandığında zoom animasyonu
                      _mapController.move(point, _mapController.camera.zoom + 1);
                    },
                  ),
                  children: [
                    // Base map layer
                    TileLayer(
                      urlTemplate: _isSatelliteView
                          ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
                          : 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                      userAgentPackageName: 'com.farmai.app.farm_ai_app',
                      maxZoom: 18,
                    ),

                    // Polygons layer
                    PolygonLayer(
                      polygons: _polygons,
                    ),

                    // Markers layer
                    MarkerLayer(
                      markers: _markers,
                    ),

                    // Current location marker
                    if (_currentPosition != null)
                      MarkerLayer(
                        markers: [
                          Marker(
                            point: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
                            width: 20,
                            height: 20,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white, width: 2),
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                
                // Field count info
                Positioned(
                  top: 16,
                  left: 16,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.agriculture,
                          color: Colors.green.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${_fields.length} Tarla',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Zoom Controls
                Positioned(
                  bottom: 100,
                  right: 16,
                  child: Column(
                    children: [
                      // Zoom In
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: IconButton(
                          onPressed: () {
                            final currentZoom = _mapController.camera.zoom;
                            if (currentZoom < 20.0) {
                              _mapController.move(
                                _mapController.camera.center,
                                currentZoom + 1,
                              );
                            }
                          },
                          icon: Icon(
                            Icons.add,
                            color: Colors.green.shade700,
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Zoom Out
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: IconButton(
                          onPressed: () {
                            final currentZoom = _mapController.camera.zoom;
                            if (currentZoom > 3.0) {
                              _mapController.move(
                                _mapController.camera.center,
                                currentZoom - 1,
                              );
                            }
                          },
                          icon: Icon(
                            Icons.remove,
                            color: Colors.green.shade700,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewField,
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_location),
        label: const Text('Yeni Tarla'),
      ),
    );
  }
}
