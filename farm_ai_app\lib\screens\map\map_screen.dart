import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import '../../services/location_service.dart';
import '../../services/field_service.dart';
import '../../models/field.dart';
import 'field_creation_screen.dart';
import 'field_details_screen.dart';

class MapScreen extends StatefulWidget {
  const MapScreen({super.key});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final MapController _mapController = MapController();
  final LocationService _locationService = LocationService();
  final FieldService _fieldService = FieldService();

  Position? _currentPosition;
  List<Polygon> _polygons = [];
  List<Marker> _markers = [];
  List<Field> _fields = [];
  bool _isLoading = true;
  bool _isSatelliteView = false;

  // Location search state
  String _selectedProvince = '';
  String _selectedDistrict = '';
  String _selectedNeighborhood = '';
  bool _showLocationSearch = false;

  // Available locations
  final Map<String, List<String>> _provinces = {
    'İstanbul': ['Güngören', 'Bakırköy', 'Beyoğlu', 'Kadıköy', 'Beşiktaş', 'Şişli'],
    'Ankara': ['Çankaya', 'Keçiören', 'Yenimahalle', 'Mamak', 'Sincan'],
    'İzmir': ['Konak', 'Karşıyaka', 'Bornova', 'Buca', 'Çiğli'],
    'Sivas': ['Merkez', 'Kangal', 'Divriği', 'Hafik', 'Yıldızeli'],
  };

  final Map<String, Map<String, List<String>>> _neighborhoods = {
    'İstanbul': {
      'Güngören': ['Merkez Mahallesi', 'Tozkoparan Mahallesi', 'Haznedar Mahallesi'],
      'Bakırköy': ['Ataköy', 'Yeşilköy', 'Florya'],
      'Beyoğlu': ['Taksim', 'Galata', 'Karaköy'],
      'Kadıköy': ['Moda', 'Fenerbahçe', 'Bostancı'],
    },
    'Ankara': {
      'Çankaya': ['Kızılay', 'Bahçelievler', 'Çayyolu'],
      'Keçiören': ['Etlik', 'Ovacık', 'Bağlarbaşı'],
    },
    'Sivas': {
      'Merkez': ['Kale Mahallesi', 'Mimar Sinan Mahallesi', 'Yeşilyurt Mahallesi'],
      'Kangal': ['Merkez', 'Balıklı', 'Çetinkaya'],
    },
  };

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    await _getCurrentLocation();
    await _loadFields();
    if (mounted) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null && mounted) {
        setState(() => _currentPosition = position);
      }
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  Future<void> _loadFields() async {
    try {
      final fields = await _fieldService.getUserFields();
      if (mounted) {
        setState(() {
          _fields = fields;
          _updateMapPolygons();
        });
      }
    } catch (e) {
      print('Error loading fields: $e');
    }
  }

  void _updateMapPolygons() {
    List<Polygon> polygons = [];
    List<Marker> markers = [];

    for (int i = 0; i < _fields.length; i++) {
      final field = _fields[i];

      List<LatLng> points = field.areaCoordinates
          .map((coord) => LatLng(coord.latitude, coord.longitude))
          .toList();

      if (points.isNotEmpty) {
        polygons.add(
          Polygon(
            points: points,
            color: Colors.green.withOpacity(0.3),
            borderColor: Colors.green,
            borderStrokeWidth: 2,
          ),
        );

        double centerLat = points.map((p) => p.latitude).reduce((a, b) => a + b) / points.length;
        double centerLng = points.map((p) => p.longitude).reduce((a, b) => a + b) / points.length;

        markers.add(
          Marker(
            point: LatLng(centerLat, centerLng),
            width: 80,
            height: 80,
            child: GestureDetector(
              onTap: () => _showFieldDetails(field),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.agriculture,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ),
        );
      }
    }

    if (mounted) {
      setState(() {
        _polygons = polygons;
        _markers = markers;
      });
    }
  }

  void _showFieldDetails(Field field) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FieldDetailsScreen(field: field),
      ),
    ).then((_) => _loadFields());
  }

  void _toggleLocationSearch() {
    setState(() {
      _showLocationSearch = !_showLocationSearch;
      if (!_showLocationSearch) {
        _selectedProvince = '';
        _selectedDistrict = '';
        _selectedNeighborhood = '';
      }
    });
  }

  void _selectProvince(String province) {
    setState(() {
      _selectedProvince = province;
      _selectedDistrict = '';
      _selectedNeighborhood = '';
    });
    _searchAndMoveToLocation(province);
  }

  void _selectDistrict(String district) {
    setState(() {
      _selectedDistrict = district;
      _selectedNeighborhood = '';
    });
    _searchAndMoveToLocation('$_selectedProvince $district');
  }

  void _selectNeighborhood(String neighborhood) {
    setState(() {
      _selectedNeighborhood = neighborhood;
    });
    _searchAndMoveToLocation('$_selectedProvince $_selectedDistrict $neighborhood');
  }

  Future<void> _searchAndMoveToLocation(String query) async {
    try {
      final results = await _locationService.searchLocation(query);
      if (results.isNotEmpty && mounted) {
        final location = results.first;
        _mapController.move(
          LatLng(location['latitude'], location['longitude']),
          15.0,
        );
      }
    } catch (e) {
      print('Error searching location: $e');
    }
  }

  void _createNewField() {
    if (_selectedProvince.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => FieldCreationScreen(
            initialLocation: _selectedProvince.isNotEmpty
                ? '$_selectedProvince $_selectedDistrict $_selectedNeighborhood'.trim()
                : null,
          ),
        ),
      ).then((_) => _loadFields());
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Önce bir konum seçin (İl > İlçe > Mahalle)'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _toggleMapType() {
    setState(() {
      _isSatelliteView = !_isSatelliteView;
    });
  }

  void _goToCurrentLocation() async {
    await _getCurrentLocation();
    if (_currentPosition != null) {
      _mapController.move(
        LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        15.0,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: SafeArea(
        child: Column(
          children: [
            // Modern Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade600, Colors.green.shade700],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Title and controls
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.map,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Text(
                          'Tarla Haritası',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: _toggleLocationSearch,
                        icon: Icon(
                          _showLocationSearch ? Icons.close : Icons.search,
                          color: Colors.white,
                        ),
                        tooltip: 'Konum Ara',
                      ),
                      IconButton(
                        onPressed: _toggleMapType,
                        icon: Icon(
                          _isSatelliteView ? Icons.map : Icons.satellite,
                          color: Colors.white,
                        ),
                        tooltip: 'Harita Türü',
                      ),
                      IconButton(
                        onPressed: _goToCurrentLocation,
                        icon: const Icon(
                          Icons.my_location,
                          color: Colors.white,
                        ),
                        tooltip: 'Konumum',
                      ),
                    ],
                  ),

                  // Location search section
                  if (_showLocationSearch) ...[
                    const SizedBox(height: 20),
                    _buildLocationSearchSection(),
                  ],
                ],
              ),
            ),

            // Map
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildMap(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewField,
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_location),
        label: Text(_selectedProvince.isNotEmpty ? 'Tarla Çiz' : 'Konum Seç'),
      ),
    );
  }

  Widget _buildLocationSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Province selection
          Text(
            '1. İl Seçin',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _provinces.keys.map((province) {
              final isSelected = _selectedProvince == province;
              return GestureDetector(
                onTap: () => _selectProvince(province),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.white : Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? Colors.green.shade700 : Colors.white.withOpacity(0.5),
                    ),
                  ),
                  child: Text(
                    province,
                    style: TextStyle(
                      color: isSelected ? Colors.green.shade700 : Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          // District selection
          if (_selectedProvince.isNotEmpty && _provinces[_selectedProvince] != null) ...[
            const SizedBox(height: 16),
            Text(
              '2. İlçe Seçin',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _provinces[_selectedProvince]!.map((district) {
                final isSelected = _selectedDistrict == district;
                return GestureDetector(
                  onTap: () => _selectDistrict(district),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.white : Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? Colors.green.shade700 : Colors.white.withOpacity(0.5),
                      ),
                    ),
                    child: Text(
                      district,
                      style: TextStyle(
                        color: isSelected ? Colors.green.shade700 : Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],

          // Neighborhood selection
          if (_selectedDistrict.isNotEmpty &&
              _neighborhoods[_selectedProvince]?[_selectedDistrict] != null) ...[
            const SizedBox(height: 16),
            Text(
              '3. Mahalle Seçin (İsteğe Bağlı)',
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _neighborhoods[_selectedProvince]![_selectedDistrict]!.map((neighborhood) {
                final isSelected = _selectedNeighborhood == neighborhood;
                return GestureDetector(
                  onTap: () => _selectNeighborhood(neighborhood),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.white : Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? Colors.green.shade700 : Colors.white.withOpacity(0.5),
                      ),
                    ),
                    child: Text(
                      neighborhood,
                      style: TextStyle(
                        color: isSelected ? Colors.green.shade700 : Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMap() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            initialCenter: _currentPosition != null
                ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
                : const LatLng(39.9334, 32.8597),
            initialZoom: 15.0,
            minZoom: 3.0,
            maxZoom: 20.0,
            interactionOptions: const InteractionOptions(
              flags: InteractiveFlag.all,
              enableMultiFingerGestureRace: true,
            ),
          ),
          children: [
            TileLayer(
              urlTemplate: _isSatelliteView
                  ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
                  : 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName: 'com.farmai.app.farm_ai_app',
              maxZoom: 18,
            ),
            PolygonLayer(polygons: _polygons),
            MarkerLayer(markers: _markers),
            if (_currentPosition != null)
              MarkerLayer(
                markers: [
                  Marker(
                    point: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
                    width: 20,
                    height: 20,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}