import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:intl/intl.dart';
import '../../models/field.dart';
import '../../services/field_service.dart';

class FieldDetailsScreen extends StatefulWidget {
  final Field field;

  const FieldDetailsScreen({super.key, required this.field});

  @override
  State<FieldDetailsScreen> createState() => _FieldDetailsScreenState();
}

class _FieldDetailsScreenState extends State<FieldDetailsScreen> {
  final FieldService _fieldService = FieldService();
  late Field _field;
  bool _isEditing = false;
  
  final _nameController = TextEditingController();
  final _cropTypeController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime? _plantingDate;
  DateTime? _harvestDate;

  final List<String> _cropTypes = [
    'Buğday', 'Arpa', '<PERSON>ı<PERSON>ır', '<PERSON><PERSON><PERSON><PERSON><PERSON>e<PERSON><PERSON>', 'Soya', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Dom<PERSON>', '<PERSON><PERSON>', '<PERSON>lı<PERSON>',
    '<PERSON>atalık', 'Soğan', 'Sarımsak', 'Fasulye', 'Nohut',
    'Mercimek', 'Üzüm', 'Elma', 'Armut', 'Kiraz', 'Kayısı',
    'Şeftali', 'Ceviz', 'Fındık', 'Antep Fıstığı', 'Zeytin', 'Diğer',
  ];

  @override
  void initState() {
    super.initState();
    _field = widget.field;
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController.text = _field.name;
    _cropTypeController.text = _field.cropType ?? '';
    _notesController.text = _field.notes ?? '';
    _plantingDate = _field.plantingDate;
    _harvestDate = _field.harvestDate;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _cropTypeController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isPlantingDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isPlantingDate 
          ? (_plantingDate ?? DateTime.now())
          : (_harvestDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    
    if (picked != null) {
      setState(() {
        if (isPlantingDate) {
          _plantingDate = picked;
        } else {
          _harvestDate = picked;
        }
      });
    }
  }

  Future<void> _saveChanges() async {
    try {
      final updatedField = _field.copyWith(
        name: _nameController.text,
        cropType: _cropTypeController.text.isNotEmpty ? _cropTypeController.text : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        plantingDate: _plantingDate,
        harvestDate: _harvestDate,
      );

      final success = await _fieldService.updateField(updatedField);
      
      if (success) {
        setState(() {
          _field = updatedField;
          _isEditing = false;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tarla bilgileri güncellendi'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Güncelleme sırasında hata oluştu'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hata: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteField() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tarlayı Sil'),
        content: Text('${_field.name} tarlasını silmek istediğinizden emin misiniz?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Sil'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await _fieldService.deleteField(_field.id!);
        
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tarla silindi'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Silme işlemi sırasında hata oluştu'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Hata: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Convert FieldCoordinate to LatLng for map display
    List<LatLng> polygonPoints = _field.areaCoordinates
        .map((coord) => LatLng(coord.latitude, coord.longitude))
        .toList();

    // Calculate center point for camera
    double centerLat = polygonPoints.map((p) => p.latitude).reduce((a, b) => a + b) / polygonPoints.length;
    double centerLng = polygonPoints.map((p) => p.longitude).reduce((a, b) => a + b) / polygonPoints.length;

    return Scaffold(
      appBar: AppBar(
        title: Text(_field.name),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          if (_isEditing) ...[
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveChanges,
            ),
            IconButton(
              icon: const Icon(Icons.cancel),
              onPressed: () {
                setState(() {
                  _isEditing = false;
                  _initializeControllers();
                });
              },
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => setState(() => _isEditing = true),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteField,
            ),
          ],
        ],
      ),
      body: Column(
        children: [
          // Map view
          Container(
            height: 200,
            child: FlutterMap(
              options: MapOptions(
                initialCenter: LatLng(centerLat, centerLng),
                initialZoom: 16.0,
              ),
              children: [
                TileLayer(
                  urlTemplate: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
                  userAgentPackageName: 'com.farmai.app.farm_ai_app',
                ),
                PolygonLayer(
                  polygons: [
                    Polygon(
                      points: polygonPoints,
                      color: Colors.green.withOpacity(0.3),
                      borderColor: Colors.green,
                      borderStrokeWidth: 2,
                    ),
                  ],
                ),
                MarkerLayer(
                  markers: [
                    Marker(
                      point: LatLng(centerLat, centerLng),
                      width: 60,
                      height: 60,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.agriculture,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Field details
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic info card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Temel Bilgiler',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          if (_isEditing) ...[
                            TextField(
                              controller: _nameController,
                              decoration: const InputDecoration(
                                labelText: 'Tarla Adı',
                                border: OutlineInputBorder(),
                              ),
                            ),
                            const SizedBox(height: 16),
                            
                            DropdownButtonFormField<String>(
                              value: _cropTypeController.text.isNotEmpty ? _cropTypeController.text : null,
                              decoration: const InputDecoration(
                                labelText: 'Ürün Türü',
                                border: OutlineInputBorder(),
                              ),
                              items: _cropTypes.map((crop) => DropdownMenuItem(
                                value: crop,
                                child: Text(crop),
                              )).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  _cropTypeController.text = value;
                                }
                              },
                            ),
                          ] else ...[
                            _buildInfoRow('Tarla Adı', _field.name),
                            _buildInfoRow('Alan', '${_field.areaSize.toStringAsFixed(2)} hektar'),
                            _buildInfoRow('Ürün Türü', _field.cropType ?? 'Belirtilmemiş'),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Dates card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Tarihler',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          if (_isEditing) ...[
                            ListTile(
                              title: const Text('Ekim Tarihi'),
                              subtitle: Text(_plantingDate != null 
                                  ? DateFormat('dd/MM/yyyy').format(_plantingDate!)
                                  : 'Seçilmemiş'),
                              trailing: const Icon(Icons.calendar_today),
                              onTap: () => _selectDate(context, true),
                            ),
                            ListTile(
                              title: const Text('Hasat Tarihi'),
                              subtitle: Text(_harvestDate != null 
                                  ? DateFormat('dd/MM/yyyy').format(_harvestDate!)
                                  : 'Seçilmemiş'),
                              trailing: const Icon(Icons.calendar_today),
                              onTap: () => _selectDate(context, false),
                            ),
                          ] else ...[
                            _buildInfoRow('Ekim Tarihi', _field.plantingDate != null 
                                ? DateFormat('dd/MM/yyyy').format(_field.plantingDate!)
                                : 'Belirtilmemiş'),
                            _buildInfoRow('Hasat Tarihi', _field.harvestDate != null 
                                ? DateFormat('dd/MM/yyyy').format(_field.harvestDate!)
                                : 'Belirtilmemiş'),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Notes card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Notlar',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          if (_isEditing) ...[
                            TextField(
                              controller: _notesController,
                              decoration: const InputDecoration(
                                labelText: 'Notlar',
                                border: OutlineInputBorder(),
                              ),
                              maxLines: 3,
                            ),
                          ] else ...[
                            Text(_field.notes?.isNotEmpty == true 
                                ? _field.notes!
                                : 'Not eklenmemiş'),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
