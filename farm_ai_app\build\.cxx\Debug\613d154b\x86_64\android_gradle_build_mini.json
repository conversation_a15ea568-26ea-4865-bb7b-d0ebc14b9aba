{"buildFiles": ["C:\\DEV\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\asd\\farm_ai_app\\build\\.cxx\\Debug\\613d154b\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\asd\\farm_ai_app\\build\\.cxx\\Debug\\613d154b\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}