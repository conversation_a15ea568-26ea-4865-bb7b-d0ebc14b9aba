^C:\USERS\<USER>\DESKTOP\ASD\FARM_AI_APP\BUILD\WINDOWS\X64\CMAKEFILES\CA9FE29B7705D69DE289EC6756E54F9A\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/asd/farm_ai_app/windows -BC:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/farm_ai_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
