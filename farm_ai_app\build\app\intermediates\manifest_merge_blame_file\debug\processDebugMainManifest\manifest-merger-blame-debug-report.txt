1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.farmai.app.farm_ai_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:4:5-79
16-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
17-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:5:5-81
17-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:5:22-78
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:6:5-65
18-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:6:22-62
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:7:5-81
19-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:7:22-78
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:8:5-80
20-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:8:22-77
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:51:5-56:15
29        <intent>
29-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:52:9-55:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:53:13-72
30-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:53:21-70
31
32            <data android:mimeType="text/plain" />
32-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:54:13-50
32-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:54:19-48
33        </intent>
34        <!-- Needs to be explicitly declared on Android R+ -->
35        <package android:name="com.google.android.apps.maps" />
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
36    </queries> <!-- Include required permissions for Google Maps API to run. -->
37    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
37-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
37-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
38
39    <uses-feature
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
40        android:glEsVersion="0x00020000"
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
41        android:required="true" />
41-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.farmai.app.farm_ai_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.farmai.app.farm_ai_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="true"
54        android:icon="@mipmap/ic_launcher"
55        android:label="Tarım AI" >
56        <activity
57            android:name="com.farmai.app.farm_ai_app.MainActivity"
58            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
59            android:exported="true"
60            android:hardwareAccelerated="true"
61            android:launchMode="singleTop"
62            android:taskAffinity=""
63            android:theme="@style/LaunchTheme"
64            android:windowSoftInputMode="adjustResize" >
65
66            <!--
67                 Specifies an Android theme to apply to this Activity as soon as
68                 the Android process has started. This theme is visible to the user
69                 while the Flutter UI initializes. After that, this theme continues
70                 to determine the Window background behind the Flutter UI.
71            -->
72            <meta-data
73                android:name="io.flutter.embedding.android.NormalTheme"
74                android:resource="@style/NormalTheme" />
75
76            <intent-filter>
77                <action android:name="android.intent.action.MAIN" />
78
79                <category android:name="android.intent.category.LAUNCHER" />
80            </intent-filter>
81        </activity>
82        <!--
83             Don't delete the meta-data below.
84             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
85        -->
86        <meta-data
87            android:name="flutterEmbedding"
88            android:value="2" />
89        <!-- Google Maps API Key -->
90        <meta-data
91            android:name="com.google.android.geo.API_KEY"
92            android:value="YOUR_GOOGLE_MAPS_API_KEY_HERE" />
93
94        <service
94-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
95            android:name="com.baseflow.geolocator.GeolocatorLocationService"
95-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
96            android:enabled="true"
96-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
97            android:exported="false"
97-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
98            android:foregroundServiceType="location" />
98-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
99        <service
99-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
100            android:name="com.lyokone.location.FlutterLocationService"
100-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
101            android:enabled="true"
101-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
102            android:exported="false"
102-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
103            android:foregroundServiceType="location" />
103-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
104
105        <provider
105-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
106            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
106-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
107            android:authorities="com.farmai.app.farm_ai_app.flutter.image_provider"
107-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
108            android:exported="false"
108-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
109            android:grantUriPermissions="true" >
109-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
110            <meta-data
110-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
112                android:resource="@xml/flutter_image_picker_file_paths" />
112-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
113        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
114        <service
114-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
115            android:name="com.google.android.gms.metadata.ModuleDependencies"
115-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
116            android:enabled="false"
116-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
117            android:exported="false" >
117-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
118            <intent-filter>
118-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
119                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
119-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
119-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
120            </intent-filter>
121
122            <meta-data
122-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
123                android:name="photopicker_activity:0:required"
123-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
124                android:value="" />
124-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
125        </service> <!-- Needs to be explicitly declared on P+ -->
126        <uses-library
126-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
127            android:name="org.apache.http.legacy"
127-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
128            android:required="false" />
128-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
129
130        <activity
130-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
131            android:name="com.google.android.gms.common.api.GoogleApiActivity"
131-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
132            android:exported="false"
132-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
133            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
133-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
134
135        <meta-data
135-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
136            android:name="com.google.android.gms.version"
136-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
137            android:value="@integer/google_play_services_version" />
137-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
138
139        <uses-library
139-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
140            android:name="androidx.window.extensions"
140-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
141            android:required="false" />
141-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
142        <uses-library
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
143            android:name="androidx.window.sidecar"
143-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
144            android:required="false" />
144-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
145
146        <provider
146-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
147            android:name="androidx.startup.InitializationProvider"
147-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
148            android:authorities="com.farmai.app.farm_ai_app.androidx-startup"
148-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
149            android:exported="false" >
149-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
150            <meta-data
150-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
151                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
151-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
152                android:value="androidx.startup" />
152-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
153            <meta-data
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
155                android:value="androidx.startup" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
156        </provider>
157
158        <receiver
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
159            android:name="androidx.profileinstaller.ProfileInstallReceiver"
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
160            android:directBootAware="false"
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
161            android:enabled="true"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
162            android:exported="true"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
163            android:permission="android.permission.DUMP" >
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
164            <intent-filter>
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
165                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
166            </intent-filter>
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
168                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
171                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
174                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
175            </intent-filter>
176        </receiver>
177    </application>
178
179</manifest>
