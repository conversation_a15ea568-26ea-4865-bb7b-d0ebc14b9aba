1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.farmai.app.farm_ai_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
16-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:4:5-79
16-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:4:22-76
17    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
17-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:5:5-81
17-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:5:22-78
18    <uses-permission android:name="android.permission.CAMERA" />
18-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:6:5-65
18-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:6:22-62
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:7:5-81
19-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:7:22-78
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:8:5-80
20-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:8:22-77
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:47:5-52:15
29        <intent>
29-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:48:9-51:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:49:13-72
30-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:49:21-70
31
32            <data android:mimeType="text/plain" />
32-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:50:13-50
32-->C:\Users\<USER>\Desktop\asd\farm_ai_app\android\app\src\main\AndroidManifest.xml:50:19-48
33        </intent>
34    </queries>
35
36    <permission
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
37        android:name="com.farmai.app.farm_ai_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.farmai.app.farm_ai_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
41
42    <application
43        android:name="android.app.Application"
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
45        android:debuggable="true"
46        android:extractNativeLibs="true"
47        android:icon="@mipmap/ic_launcher"
48        android:label="Tarım AI" >
49        <activity
50            android:name="com.farmai.app.farm_ai_app.MainActivity"
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
52            android:exported="true"
53            android:hardwareAccelerated="true"
54            android:launchMode="singleTop"
55            android:taskAffinity=""
56            android:theme="@style/LaunchTheme"
57            android:windowSoftInputMode="adjustResize" >
58
59            <!--
60                 Specifies an Android theme to apply to this Activity as soon as
61                 the Android process has started. This theme is visible to the user
62                 while the Flutter UI initializes. After that, this theme continues
63                 to determine the Window background behind the Flutter UI.
64            -->
65            <meta-data
66                android:name="io.flutter.embedding.android.NormalTheme"
67                android:resource="@style/NormalTheme" />
68
69            <intent-filter>
70                <action android:name="android.intent.action.MAIN" />
71
72                <category android:name="android.intent.category.LAUNCHER" />
73            </intent-filter>
74        </activity>
75        <!--
76             Don't delete the meta-data below.
77             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
78        -->
79        <meta-data
80            android:name="flutterEmbedding"
81            android:value="2" />
82
83        <service
83-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
84            android:name="com.baseflow.geolocator.GeolocatorLocationService"
84-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
85            android:enabled="true"
85-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
86            android:exported="false"
86-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
87            android:foregroundServiceType="location" />
87-->[:geolocator_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
88        <service
88-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
89            android:name="com.lyokone.location.FlutterLocationService"
89-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
90            android:enabled="true"
90-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
91            android:exported="false"
91-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
92            android:foregroundServiceType="location" />
92-->[:location] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
93
94        <provider
94-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
95            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
95-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
96            android:authorities="com.farmai.app.farm_ai_app.flutter.image_provider"
96-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
97            android:exported="false"
97-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
98            android:grantUriPermissions="true" >
98-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
99            <meta-data
99-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
100                android:name="android.support.FILE_PROVIDER_PATHS"
100-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
101                android:resource="@xml/flutter_image_picker_file_paths" />
101-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
102        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
103        <service
103-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
104            android:name="com.google.android.gms.metadata.ModuleDependencies"
104-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
105            android:enabled="false"
105-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
106            android:exported="false" >
106-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
107            <intent-filter>
107-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
108                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
108-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
108-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
109            </intent-filter>
110
111            <meta-data
111-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
112                android:name="photopicker_activity:0:required"
112-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
113                android:value="" />
113-->[:image_picker_android] C:\Users\<USER>\Desktop\asd\farm_ai_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
114        </service>
115
116        <activity
116-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
117            android:name="com.google.android.gms.common.api.GoogleApiActivity"
117-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
118            android:exported="false"
118-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
119            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
119-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
120
121        <meta-data
121-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
122            android:name="com.google.android.gms.version"
122-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
123            android:value="@integer/google_play_services_version" />
123-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
124
125        <uses-library
125-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
126            android:name="androidx.window.extensions"
126-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
127            android:required="false" />
127-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
128        <uses-library
128-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
129            android:name="androidx.window.sidecar"
129-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
130            android:required="false" />
130-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
131
132        <provider
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
133            android:name="androidx.startup.InitializationProvider"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
134            android:authorities="com.farmai.app.farm_ai_app.androidx-startup"
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
135            android:exported="false" >
135-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
136            <meta-data
136-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
137-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
138                android:value="androidx.startup" />
138-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
139            <meta-data
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
140                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
141                android:value="androidx.startup" />
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
142        </provider>
143
144        <receiver
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
145            android:name="androidx.profileinstaller.ProfileInstallReceiver"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
146            android:directBootAware="false"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
147            android:enabled="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
148            android:exported="true"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
149            android:permission="android.permission.DUMP" >
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
151                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
152            </intent-filter>
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
154                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
157                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
160                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
161            </intent-filter>
162        </receiver>
163    </application>
164
165</manifest>
