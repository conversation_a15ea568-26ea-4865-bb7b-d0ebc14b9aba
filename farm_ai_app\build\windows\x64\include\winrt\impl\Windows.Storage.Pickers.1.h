// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.210806.1

#pragma once
#ifndef WINRT_Windows_Storage_Pickers_1_H
#define WINRT_Windows_Storage_Pickers_1_H
#include "winrt/impl/Windows.Storage.Pickers.0.h"
WINRT_EXPORT namespace winrt::Windows::Storage::Pickers
{
    struct __declspec(empty_bases) IFileOpenPicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileOpenPicker>
    {
        IFileOpenPicker(std::nullptr_t = nullptr) noexcept {}
        IFileOpenPicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileOpenPicker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileOpenPicker2>
    {
        IFileOpenPicker2(std::nullptr_t = nullptr) noexcept {}
        IFileOpenPicker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileOpenPicker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileOpenPicker3>
    {
        IFileOpenPicker3(std::nullptr_t = nullptr) noexcept {}
        IFileOpenPicker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileOpenPickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileOpenPickerStatics>
    {
        IFileOpenPickerStatics(std::nullptr_t = nullptr) noexcept {}
        IFileOpenPickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileOpenPickerStatics2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileOpenPickerStatics2>
    {
        IFileOpenPickerStatics2(std::nullptr_t = nullptr) noexcept {}
        IFileOpenPickerStatics2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileOpenPickerWithOperationId :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileOpenPickerWithOperationId>
    {
        IFileOpenPickerWithOperationId(std::nullptr_t = nullptr) noexcept {}
        IFileOpenPickerWithOperationId(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileSavePicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileSavePicker>
    {
        IFileSavePicker(std::nullptr_t = nullptr) noexcept {}
        IFileSavePicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileSavePicker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileSavePicker2>
    {
        IFileSavePicker2(std::nullptr_t = nullptr) noexcept {}
        IFileSavePicker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileSavePicker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileSavePicker3>
    {
        IFileSavePicker3(std::nullptr_t = nullptr) noexcept {}
        IFileSavePicker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileSavePicker4 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileSavePicker4>
    {
        IFileSavePicker4(std::nullptr_t = nullptr) noexcept {}
        IFileSavePicker4(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFileSavePickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFileSavePickerStatics>
    {
        IFileSavePickerStatics(std::nullptr_t = nullptr) noexcept {}
        IFileSavePickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFolderPicker :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFolderPicker>
    {
        IFolderPicker(std::nullptr_t = nullptr) noexcept {}
        IFolderPicker(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFolderPicker2 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFolderPicker2>
    {
        IFolderPicker2(std::nullptr_t = nullptr) noexcept {}
        IFolderPicker2(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFolderPicker3 :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFolderPicker3>
    {
        IFolderPicker3(std::nullptr_t = nullptr) noexcept {}
        IFolderPicker3(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
    struct __declspec(empty_bases) IFolderPickerStatics :
        winrt::Windows::Foundation::IInspectable,
        impl::consume_t<IFolderPickerStatics>
    {
        IFolderPickerStatics(std::nullptr_t = nullptr) noexcept {}
        IFolderPickerStatics(void* ptr, take_ownership_from_abi_t) noexcept : winrt::Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
