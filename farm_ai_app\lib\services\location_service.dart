import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  // Check and request location permissions
  Future<bool> requestLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return false;
      }

      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return false;
      }

      return true;
    } catch (e) {
      print('Location permission error: $e');
      return false;
    }
  }

  // Get current location
  Future<Position?> getCurrentLocation() async {
    try {
      bool hasPermission = await requestLocationPermission();
      if (!hasPermission) return null;

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      return position;
    } catch (e) {
      print('Get current location error: $e');
      return null;
    }
  }

  // Get address from coordinates
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        return '${place.street}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea}';
      }
      return null;
    } catch (e) {
      print('Get address error: $e');
      return null;
    }
  }

  // Get coordinates from address
  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        Location location = locations[0];
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }
      return null;
    } catch (e) {
      print('Get coordinates error: $e');
      return null;
    }
  }

  // Manuel konum ekleme
  Future<Position?> getLocationFromCoordinates(double latitude, double longitude) async {
    try {
      return Position(
        latitude: latitude,
        longitude: longitude,
        timestamp: DateTime.now(),
        accuracy: 0.0,
        altitude: 0.0,
        altitudeAccuracy: 0.0,
        heading: 0.0,
        headingAccuracy: 0.0,
        speed: 0.0,
        speedAccuracy: 0.0,
      );
    } catch (e) {
      print('Error creating position from coordinates: $e');
      return null;
    }
  }

  // Adres arama
  Future<List<Map<String, dynamic>>> searchLocation(String query) async {
    try {
      List<Map<String, dynamic>> results = [];

      // Türkiye'nin bazı önemli şehirleri
      Map<String, Map<String, double>> cities = {
        'ankara': {'lat': 39.9334, 'lng': 32.8597},
        'istanbul': {'lat': 41.0082, 'lng': 28.9784},
        'izmir': {'lat': 38.4192, 'lng': 27.1287},
        'bursa': {'lat': 40.1826, 'lng': 29.0665},
        'antalya': {'lat': 36.8969, 'lng': 30.7133},
        'adana': {'lat': 37.0000, 'lng': 35.3213},
        'konya': {'lat': 37.8667, 'lng': 32.4833},
        'gaziantep': {'lat': 37.0662, 'lng': 37.3833},
        'kayseri': {'lat': 38.7312, 'lng': 35.4787},
        'eskişehir': {'lat': 39.7767, 'lng': 30.5206},
      };

      String lowerQuery = query.toLowerCase();

      cities.forEach((city, coords) {
        if (city.contains(lowerQuery) || lowerQuery.contains(city)) {
          results.add({
            'name': city.toUpperCase(),
            'description': '$city, Türkiye',
            'latitude': coords['lat'],
            'longitude': coords['lng'],
          });
        }
      });

      return results;
    } catch (e) {
      print('Error searching location: $e');
      return [];
    }
  }

  // Calculate distance between two points
  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Calculate area of polygon (in square meters)
  double calculatePolygonArea(List<Position> coordinates) {
    if (coordinates.length < 3) return 0;

    double area = 0;
    int j = coordinates.length - 1;

    for (int i = 0; i < coordinates.length; i++) {
      area += (coordinates[j].longitude + coordinates[i].longitude) *
          (coordinates[j].latitude - coordinates[i].latitude);
      j = i;
    }

    return (area.abs() / 2) * 111320 * 111320; // Convert to square meters
  }

  // Convert square meters to hectares
  double squareMetersToHectares(double squareMeters) {
    return squareMeters / 10000;
  }

  // Convert square meters to acres
  double squareMetersToAcres(double squareMeters) {
    return squareMeters / 4047;
  }

  // Check if point is inside polygon
  bool isPointInPolygon(Position point, List<Position> polygon) {
    int intersectCount = 0;
    for (int j = 0, i = 1; i < polygon.length; j = i++) {
      if (((polygon[i].latitude > point.latitude) != (polygon[j].latitude > point.latitude)) &&
          (point.longitude < (polygon[j].longitude - polygon[i].longitude) * 
           (point.latitude - polygon[i].latitude) / 
           (polygon[j].latitude - polygon[i].latitude) + polygon[i].longitude)) {
        intersectCount++;
      }
    }
    return (intersectCount % 2) == 1;
  }
}
