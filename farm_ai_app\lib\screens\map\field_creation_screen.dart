import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:geolocator/geolocator.dart';
import '../../services/location_service.dart';
import '../../services/field_service.dart';
import '../../models/field.dart';

class FieldCreationScreen extends StatefulWidget {
  final String? initialLocation;

  const FieldCreationScreen({
    super.key,
    this.initialLocation,
  });

  @override
  State<FieldCreationScreen> createState() => _FieldCreationScreenState();
}

class _FieldCreationScreenState extends State<FieldCreationScreen> {
  final MapController _mapController = MapController();
  final LocationService _locationService = LocationService();
  final FieldService _fieldService = FieldService();

  final _nameController = TextEditingController();
  final _cropTypeController = TextEditingController();
  final _notesController = TextEditingController();

  Position? _currentPosition;
  List<LatLng> _polygonPoints = [];
  List<Marker> _markers = [];
  Polygon? _currentPolygon;
  bool _isDrawing = false;
  double _calculatedArea = 0.0;

  final List<String> _cropTypes = [
    'Buğday', 'Arpa', 'Mısır', 'Ayçiçeği', 'Soya', 'Pamuk',
    'Şeker Pancarı', 'Patates', 'Domates', 'Biber', 'Patlıcan',
    'Salatalık', 'Soğan', 'Sarımsak', 'Fasulye', 'Nohut',
    'Mercimek', 'Üzüm', 'Elma', 'Armut', 'Kiraz', 'Kayısı',
    'Şeftali', 'Ceviz', 'Fındık', 'Antep Fıstığı', 'Zeytin', 'Diğer',
  ];

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _cropTypeController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      final position = await _locationService.getCurrentLocation();
      if (position != null) {
        setState(() => _currentPosition = position);
      }
    } catch (e) {
      print('Error getting location: $e');
    }
  }

  void _onMapTap(TapPosition tapPosition, LatLng point) {
    if (!_isDrawing) return;

    setState(() {
      _polygonPoints.add(point);
      _markers.add(
        Marker(
          point: point,
          width: 30,
          height: 30,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '${_polygonPoints.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ),
      );

      if (_polygonPoints.length >= 3) {
        _currentPolygon = Polygon(
          points: _polygonPoints,
          color: Colors.red.withOpacity(0.3),
          borderColor: Colors.red,
          borderStrokeWidth: 2,
        );
        _calculateArea();
      }
    });
  }

  void _calculateArea() {
    if (_polygonPoints.length >= 3) {
      List<FieldCoordinate> coordinates = _polygonPoints
          .map((point) => FieldCoordinate(
                latitude: point.latitude,
                longitude: point.longitude,
              ))
          .toList();

      _calculatedArea = _fieldService.calculateFieldArea(coordinates);
    }
  }

  void _startDrawing() {
    setState(() {
      _isDrawing = true;
      _polygonPoints.clear();
      _markers.clear();
      _currentPolygon = null;
      _calculatedArea = 0.0;
    });
  }

  void _stopDrawing() {
    setState(() => _isDrawing = false);
  }

  void _clearDrawing() {
    setState(() {
      _polygonPoints.clear();
      _markers.clear();
      _currentPolygon = null;
      _calculatedArea = 0.0;
      _isDrawing = false;
    });
  }

  void _undoLastPoint() {
    if (_polygonPoints.isNotEmpty) {
      setState(() {
        _polygonPoints.removeLast();
        _markers.removeLast();

        if (_polygonPoints.length >= 3) {
          _currentPolygon = Polygon(
            points: _polygonPoints,
            color: Colors.red.withOpacity(0.3),
            borderColor: Colors.red,
            borderStrokeWidth: 2,
          );
          _calculateArea();
        } else {
          _currentPolygon = null;
          _calculatedArea = 0.0;
        }
      });
    }
  }

  Future<void> _saveField() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Lütfen tarla adını girin')),
      );
      return;
    }

    if (_polygonPoints.length < 3) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('En az 3 nokta işaretleyerek tarla sınırlarını çizin')),
      );
      return;
    }

    try {
      List<FieldCoordinate> coordinates = _polygonPoints
          .map((point) => FieldCoordinate(
                latitude: point.latitude,
                longitude: point.longitude,
              ))
          .toList();

      final fieldId = await _fieldService.createField(
        name: _nameController.text,
        coordinates: coordinates,
        areaSize: _calculatedArea,
        cropType: _cropTypeController.text.isNotEmpty ? _cropTypeController.text : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      if (fieldId != null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tarla başarıyla kaydedildi'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tarla kaydedilirken hata oluştu'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hata: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Yeni Tarla Oluştur'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          if (_isDrawing)
            IconButton(
              icon: const Icon(Icons.undo),
              onPressed: _undoLastPoint,
              tooltip: 'Son Noktayı Geri Al',
            ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: _clearDrawing,
            tooltip: 'Temizle',
          ),
        ],
      ),
      body: Column(
        children: [
          // Map
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                FlutterMap(
                  mapController: _mapController,
                  options: MapOptions(
                    initialCenter: _currentPosition != null
                        ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
                        : const LatLng(39.9334, 32.8597),
                    initialZoom: 18.0,
                    minZoom: 3.0,
                    maxZoom: 20.0,
                    onTap: _onMapTap,
                    // Zoom ve pan kontrolleri
                    interactionOptions: const InteractionOptions(
                      flags: InteractiveFlag.all,
                      enableMultiFingerGestureRace: true,
                    ),
                  ),
                  children: [
                    TileLayer(
                      urlTemplate: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
                      userAgentPackageName: 'com.farmai.app.farm_ai_app',
                    ),
                    if (_currentPolygon != null)
                      PolygonLayer(polygons: [_currentPolygon!]),
                    MarkerLayer(markers: _markers),
                    if (_currentPosition != null)
                      MarkerLayer(
                        markers: [
                          Marker(
                            point: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
                            width: 20,
                            height: 20,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.white, width: 2),
                              ),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),

                // Drawing controls
                Positioned(
                  top: 16,
                  right: 16,
                  child: Column(
                    children: [
                      FloatingActionButton(
                        mini: true,
                        heroTag: "start_drawing",
                        onPressed: _isDrawing ? _stopDrawing : _startDrawing,
                        backgroundColor: _isDrawing ? Colors.red : Colors.green,
                        child: Icon(_isDrawing ? Icons.stop : Icons.edit),
                      ),
                      if (_calculatedArea > 0) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            '${_calculatedArea.toStringAsFixed(2)} ha',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Zoom Controls
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: Column(
                    children: [
                      // Zoom In
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          onPressed: () {
                            final currentZoom = _mapController.camera.zoom;
                            if (currentZoom < 20.0) {
                              _mapController.move(
                                _mapController.camera.center,
                                currentZoom + 1,
                              );
                            }
                          },
                          icon: Icon(
                            Icons.add,
                            color: Colors.green.shade700,
                            size: 20,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Zoom Out
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          onPressed: () {
                            final currentZoom = _mapController.camera.zoom;
                            if (currentZoom > 3.0) {
                              _mapController.move(
                                _mapController.camera.center,
                                currentZoom - 1,
                              );
                            }
                          },
                          icon: Icon(
                            Icons.remove,
                            color: Colors.green.shade700,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Form
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextField(
                      controller: _nameController,
                      decoration: const InputDecoration(
                        labelText: 'Tarla Adı *',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),

                    DropdownButtonFormField<String>(
                      value: _cropTypeController.text.isNotEmpty ? _cropTypeController.text : null,
                      decoration: const InputDecoration(
                        labelText: 'Ürün Türü',
                        border: OutlineInputBorder(),
                      ),
                      items: _cropTypes.map((crop) => DropdownMenuItem(
                        value: crop,
                        child: Text(crop),
                      )).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          _cropTypeController.text = value;
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    TextField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notlar',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),

                    ElevatedButton(
                      onPressed: _saveField,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green.shade700,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('Tarlayı Kaydet'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}