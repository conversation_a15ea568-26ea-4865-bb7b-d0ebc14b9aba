﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{60A5F95D-E1DF-30D9-8BF8-AB6510C67B0C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>farm_ai_app</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">farm_ai_app.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">farm_ai_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">farm_ai_app.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">farm_ai_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">farm_ai_app.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">farm_ai_app</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\geolocator_windows\Debug\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.lib;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/Debug/farm_ai_app.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/Debug/farm_ai_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\geolocator_windows\Profile\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Profile\permission_handler_windows_plugin.lib;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/Profile/farm_ai_app.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/Profile/farm_ai_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\cpp_client_wrapper\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\geolocator_windows\windows\include;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\.plugin_symlinks\permission_handler_windows\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\geolocator_windows\Release\geolocator_windows_plugin.lib;..\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.lib;C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/Release/farm_ai_app.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/Release/farm_ai_app.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/asd/farm_ai_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/asd/farm_ai_app/windows -BC:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule C:/Users/<USER>/Desktop/asd/farm_ai_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/asd/farm_ai_app/windows -BC:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/asd/farm_ai_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/asd/farm_ai_app/windows -BC:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\flutter_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\utils.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\win32_window.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{C5289050-F0DF-3C40-A2AB-2604F843E3EC}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{38E56352-6EEB-3F72-AE49-8032D47CAD6F}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{EE12CD49-CF9C-3FC7-89C9-97C3AA93C472}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{314D6371-7988-33AD-9BD7-A0D7FF6F08E8}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\plugins\geolocator_windows\geolocator_windows_plugin.vcxproj">
      <Project>{24B2D996-51A6-34F0-AC45-EF51F0417BF5}</Project>
      <Name>geolocator_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\asd\farm_ai_app\build\windows\x64\plugins\permission_handler_windows\permission_handler_windows_plugin.vcxproj">
      <Project>{77065C95-CA0E-395F-ACAA-F54B82FB3DE6}</Project>
      <Name>permission_handler_windows_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>