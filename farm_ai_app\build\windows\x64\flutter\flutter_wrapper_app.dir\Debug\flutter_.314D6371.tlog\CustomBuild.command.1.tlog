^C:\USERS\<USER>\DESKTOP\ASD\FARM_AI_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/asd/farm_ai_app/windows -BC:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64 --check-stamp-file C:/Users/<USER>/Desktop/asd/farm_ai_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
